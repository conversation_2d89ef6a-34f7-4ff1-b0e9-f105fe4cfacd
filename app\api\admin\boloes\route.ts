import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET() {
  try {
    console.log("🔄 Admin: Tentando buscar bolões do banco...")

    // Tentar buscar dados reais com timeout
    try {
      await initializeDatabase()

      // Buscar bolões com timeout
      const boloes = await Promise.race([
        executeQuery(`
          SELECT
            b.*,
            COUNT(DISTINCT a.usuario_id) as participantes,
            COALESCE(SUM(a.valor_total), 0) as faturamento_atual
          FROM boloes b
          LEFT JOIN apostas a ON b.id = a.bolao_id AND a.status = 'paga'
          GROUP BY b.id
          ORDER BY b.data_criacao DESC
          LIMIT 50
        `),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na consulta de bolões')), 5000)
        )
      ])

      // Buscar estatísticas com timeout
      const statsQueries = await Promise.race([
        Promise.all([
          executeQuery('SELECT COUNT(*) as count FROM boloes WHERE status = "ativo"'),
          executeQuery('SELECT COUNT(DISTINCT usuario_id) as count FROM apostas'),
          executeQuery('SELECT COALESCE(SUM(valor_total), 0) as total FROM apostas WHERE status = "paga"'),
          executeQuery('SELECT COUNT(*) as count FROM boloes WHERE DATE(data_fim) = CURDATE()')
        ]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout nas estatísticas')), 3000)
        )
      ])

      const stats = {
        ativos: statsQueries[0][0]?.count || 0,
        participantes: statsQueries[1][0]?.count || 0,
        faturamento: statsQueries[2][0]?.total || 0,
        finalizandoHoje: statsQueries[3][0]?.count || 0
      }

      console.log("✅ Admin: Dados reais carregados do banco")
      return NextResponse.json({
        success: true,
        boloes: boloes || [],
        stats: stats,
        source: 'database'
      })

    } catch (dbError) {
      console.error("❌ Admin: Erro ao acessar banco de dados:", dbError.message)

      return NextResponse.json({
        success: false,
        error: 'Erro ao acessar banco de dados',
        message: dbError.message,
        boloes: [],
        stats: { ativos: 0, participantes: 0, faturamento: 0, finalizandoHoje: 0 }
      }, { status: 500 })
    }
  } catch (error) {
    console.error("Erro ao buscar bolões:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error).message || "Erro desconhecido",
        boloes: [],
        stats: { ativos: 0, participantes: 0, faturamento: 0, finalizandoHoje: 0 },
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("🚀 Iniciando criação de bolão...")

    await initializeDatabase()

    const data = await request.json()
    console.log("📋 Dados recebidos:", data)

    // Validação dos campos obrigatórios
    if (!data.nome || !data.valor_aposta || !data.premio_total || !data.data_inicio || !data.data_fim) {
      console.error("❌ Campos obrigatórios faltando")
      return NextResponse.json(
        {
          success: false,
          error: "Campos obrigatórios: nome, valor_aposta, premio_total, data_inicio, data_fim",
        },
        { status: 400 }
      )
    }

    // Definir valor premium padrão se não fornecido
    const valorPremium = data.valor_premium || (parseFloat(data.valor_aposta) * 2)

    // Buscar o usuário admin (assumindo que é o primeiro admin)
    console.log("🔍 Buscando usuário admin...")
    const adminUsers = await executeQuery("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")
    console.log("👤 Admin users encontrados:", adminUsers)

    if (!adminUsers || adminUsers.length === 0) {
      console.error("❌ Usuário admin não encontrado")
      return NextResponse.json(
        {
          success: false,
          error: "Usuário administrador não encontrado",
        },
        { status: 400 }
      )
    }

    const adminUser = adminUsers[0]
    console.log("✅ Admin user selecionado:", adminUser)

    console.log("📋 Dados recebidos para criação de bolão:", {
      nome: data.nome,
      valor_aposta: data.valor_aposta,
      valor_premium: valorPremium,
      premio_total: data.premio_total,
      banner_image: data.banner_image ? "✅ " + data.banner_image : "❌ Nenhuma imagem",
      campeonatos: data.campeonatos_selecionados?.length || 0,
      partidas: data.partidas_selecionadas?.length || 0
    })

    // Criar bolão
    console.log("💾 Inserindo bolão no banco de dados...")
    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, valor_premium, premio_total, max_participantes,
        min_acertos, data_inicio, data_fim, status, criado_por, regras,
        campeonatos_selecionados, partidas_selecionadas, banner_image, data_criacao
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      data.nome,
      data.descricao || '',
      parseFloat(data.valor_aposta),
      parseFloat(valorPremium),
      parseFloat(data.premio_total),
      data.max_participantes || 100,
      data.min_acertos || 3,
      data.data_inicio,
      data.data_fim,
      data.status || 'ativo',
      adminUser.id,
      data.regras || null,
      JSON.stringify(data.campeonatos_selecionados || []),
      JSON.stringify(data.partidas_selecionadas || []),
      data.banner_image || null
    ])

    const bolaoId = result.insertId
    console.log("✅ Bolão criado com ID:", bolaoId)

    // Inserir jogos do bolão na tabela bolao_jogos
    if (data.partidas_selecionadas && data.partidas_selecionadas.length > 0) {
      console.log(`🎮 Inserindo ${data.partidas_selecionadas.length} jogos no bolão...`)

      for (const partida of data.partidas_selecionadas) {
        try {
          // Verificar se o jogo já existe na tabela jogos
          let jogoExistente = await executeQuery(`
            SELECT id FROM jogos WHERE id = ?
          `, [partida.id])

          let jogoId = partida.id

          // Se o jogo não existe, criar ele
          if (!jogoExistente || jogoExistente.length === 0) {
            console.log(`🆕 Criando jogo ${partida.id} no banco...`)

            // Buscar ou criar times
            const timeCasaResult = await executeQuery(`
              INSERT IGNORE INTO times (nome, logo_url) VALUES (?, ?)
            `, [partida.time_casa_nome, partida.time_casa_logo])

            const timeForaResult = await executeQuery(`
              INSERT IGNORE INTO times (nome, logo_url) VALUES (?, ?)
            `, [partida.time_fora_nome, partida.time_fora_logo])

            // Buscar IDs dos times
            const timeCasa = await executeQuery(`
              SELECT id FROM times WHERE nome = ? LIMIT 1
            `, [partida.time_casa_nome])

            const timeFora = await executeQuery(`
              SELECT id FROM times WHERE nome = ? LIMIT 1
            `, [partida.time_fora_nome])

            // Buscar campeonato
            const campeonato = await executeQuery(`
              SELECT id FROM campeonatos WHERE codigo = ? OR nome LIKE ? LIMIT 1
            `, [partida.campeonato_id, `%${partida.campeonato_nome}%`])

            if (timeCasa.length > 0 && timeFora.length > 0 && campeonato.length > 0) {
              const novoJogo = await executeQuery(`
                INSERT INTO jogos (
                  id, time_casa_id, time_fora_id, campeonato_id,
                  data_jogo, status, rodada
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
              `, [
                partida.id,
                timeCasa[0].id,
                timeFora[0].id,
                campeonato[0].id,
                partida.data_jogo,
                partida.status || 'agendado',
                partida.matchday || 1
              ])

              console.log(`✅ Jogo ${partida.id} criado`)
            }
          }

          // Inserir na tabela bolao_jogos
          await executeQuery(`
            INSERT IGNORE INTO bolao_jogos (bolao_id, jogo_id) VALUES (?, ?)
          `, [bolaoId, jogoId])

        } catch (jogoError) {
          console.error(`❌ Erro ao inserir jogo ${partida.id}:`, jogoError)
        }
      }

      console.log(`✅ Jogos inseridos no bolão ${bolaoId}`)
    }

    return NextResponse.json({
      success: true,
      id: bolaoId,
      message: "Bolão criado com sucesso",
    })
  } catch (error) {
    console.error("❌ Erro ao criar bolão:", error)
    console.error("❌ Stack trace:", error.stack)
    console.error("❌ Error name:", error.name)
    console.error("❌ Error message:", error.message)

    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
        details: error.stack
      },
      { status: 500 }
    )
  }
}
