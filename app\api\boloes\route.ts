import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Buscando bolões...')

    // Retornar dados mockados temporariamente para evitar problemas de conexão
    const boloesMockados = [
      {
        id: 77,
        nome: 'Bolão Multi-Campeonatos',
        descricao: 'Bolão com jogos de múltiplos campeonatos',
        valor_aposta: 25.00,
        premio_total: 10000.00,
        data_inicio: new Date().toISOString(),
        data_fim: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'ativo',
        participantes: 0,
        max_participantes: 1000,
        total_jogos: 20,
        criador: 'Admin',
        banner_image: '/uploads/banners/banner_1752645150268.webp',
        campeonatos_selecionados: [
          { id: 1, nome: 'Campeonato Brasileiro Série A' },
          { id: 33, nome: 'Premier League' },
          { id: 35, nome: 'European Championship' },
          { id: 36, nome: 'Ligue 1' }
        ],
        jogos: []
      }
    ]

    console.log(`✅ Retornando ${boloesMockados.length} bolões mockados`)

    return NextResponse.json({
      success: true,
      boloes: boloesMockados
    })

  } catch (error) {
    console.error('❌ Erro ao buscar bolões:', error.message)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message,
      boloes: []
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes,
      min_acertos,
      data_inicio,
      data_fim,
      jogos_selecionados,
      regras
    } = body

    if (!nome || !valor_aposta || !premio_total || !data_inicio || !data_fim) {
      return NextResponse.json(
        { error: 'Dados obrigatórios não fornecidos' },
        { status: 400 }
      )
    }

    // Por enquanto, vamos usar um usuário padrão como criador
    // Em produção, isso deveria vir da sessão do usuário
    const criado_por = 1

    // Inserir bolão
    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, premio_total, max_participantes,
        min_acertos, data_inicio, data_fim, status, criado_por, regras
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'em_breve', ?, ?)
    `, [
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes || null,
      min_acertos || 3,
      data_inicio,
      data_fim,
      criado_por,
      JSON.stringify(regras || [])
    ])

    const bolao_id = (result as any).insertId

    // Inserir jogos do bolão se fornecidos
    if (jogos_selecionados && jogos_selecionados.length > 0) {
      for (const jogo_id of jogos_selecionados) {
        await executeQuery(`
          INSERT INTO bolao_jogos (bolao_id, jogo_id)
          VALUES (?, ?)
        `, [bolao_id, jogo_id])
      }
    }

    return NextResponse.json({
      success: true,
      bolao_id,
      message: 'Bolão criado com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao criar bolão:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
