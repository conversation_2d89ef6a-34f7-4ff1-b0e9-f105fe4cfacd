import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    // Buscar bolões ativos
    const boloes = await executeQuery(`
      SELECT
        b.*,
        u.nome as criador_nome,
        (SELECT COUNT(*) FROM apostas WHERE bolao_id = b.id AND status = 'paga') as participantes,
        (SELECT COUNT(*) FROM bolao_jogos WHERE bolao_id = b.id) as total_jogos
      FROM boloes b
      LEFT JOIN usuarios u ON b.criado_por = u.id
      WHERE b.status IN ('ativo', 'em_breve')
      ORDER BY b.data_inicio DESC
    `)

    // Formatar dados para o frontend
    const boloesFormatted = boloes.map((bolao: any) => ({
      id: bolao.id,
      nome: bolao.nome,
      descricao: bolao.descricao,
      valor_aposta: parseFloat(bolao.valor_aposta),
      premio_total: parseFloat(bolao.premio_total),
      data_inicio: bolao.data_inicio,
      data_fim: bolao.data_fim,
      status: bolao.status,
      participantes: bolao.participantes || 0,
      max_participantes: bolao.max_participantes,
      total_jogos: bolao.total_jogos || 0,
      criador: bolao.criador_nome
    }))

    return NextResponse.json({
      success: true,
      boloes: boloesFormatted
    })

  } catch (error) {
    console.error('❌ Erro ao buscar bolões:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      boloes: []
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes,
      min_acertos,
      data_inicio,
      data_fim,
      jogos_selecionados,
      regras
    } = body

    if (!nome || !valor_aposta || !premio_total || !data_inicio || !data_fim) {
      return NextResponse.json(
        { error: 'Dados obrigatórios não fornecidos' },
        { status: 400 }
      )
    }

    // Por enquanto, vamos usar um usuário padrão como criador
    // Em produção, isso deveria vir da sessão do usuário
    const criado_por = 1

    // Inserir bolão
    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, premio_total, max_participantes,
        min_acertos, data_inicio, data_fim, status, criado_por, regras
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'em_breve', ?, ?)
    `, [
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes || null,
      min_acertos || 3,
      data_inicio,
      data_fim,
      criado_por,
      JSON.stringify(regras || [])
    ])

    const bolao_id = (result as any).insertId

    // Inserir jogos do bolão se fornecidos
    if (jogos_selecionados && jogos_selecionados.length > 0) {
      for (const jogo_id of jogos_selecionados) {
        await executeQuery(`
          INSERT INTO bolao_jogos (bolao_id, jogo_id)
          VALUES (?, ?)
        `, [bolao_id, jogo_id])
      }
    }

    return NextResponse.json({
      success: true,
      bolao_id,
      message: 'Bolão criado com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao criar bolão:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
