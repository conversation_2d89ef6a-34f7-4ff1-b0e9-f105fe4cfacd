import { initializeDatabase, executeQuery } from '../lib/database-config.js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const FOOTBALL_API_BASE = 'https://api.football-data.org/v4'
const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN

const headers = {
  'X-Auth-Token': FOOTBALL_API_TOKEN,
  'Content-Type': 'application/json'
}

/**
 * Busca dados da API Football-data.org
 */
async function fetchFootballData(endpoint) {
  try {
    console.log(`🌐 Buscando: ${FOOTBALL_API_BASE}${endpoint}`)
    
    const response = await fetch(`${FOOTBALL_API_BASE}${endpoint}`, {
      headers,
      timeout: 15000
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log(`✅ Dados recebidos: ${endpoint}`)
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error.message)
    return null
  }
}

/**
 * Sincroniza competições com escudos reais
 */
async function syncRealCompetitions() {
  try {
    console.log('🏆 Sincronizando competições com escudos reais...')
    
    const data = await fetchFootballData('/competitions')
    if (!data || !data.competitions) {
      console.log('❌ Nenhuma competição encontrada')
      return 0
    }
    
    const competitions = data.competitions
    let syncCount = 0
    
    for (const comp of competitions) {
      try {
        // Verificar se já existe
        const existing = await executeQuery(
          'SELECT id FROM campeonatos WHERE api_id = ?',
          [comp.id]
        )
        
        const competitionData = {
          nome: comp.name,
          descricao: comp.name,
          pais: comp.area?.name || 'Internacional',
          temporada: new Date().getFullYear(),
          status: 'ativo',
          data_inicio: comp.currentSeason?.startDate || new Date().toISOString().split('T')[0],
          data_fim: comp.currentSeason?.endDate || new Date(Date.now() + 365*24*60*60*1000).toISOString().split('T')[0],
          logo_url: comp.emblem || null, // Escudo real da API
          api_id: comp.id
        }
        
        if (existing.length === 0) {
          // Inserir nova competição
          await executeQuery(`
            INSERT INTO campeonatos (
              nome, descricao, pais, temporada, status, data_inicio, data_fim,
              logo_url, api_id, data_criacao
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
          `, [
            competitionData.nome,
            competitionData.descricao,
            competitionData.pais,
            competitionData.temporada,
            competitionData.status,
            competitionData.data_inicio,
            competitionData.data_fim,
            competitionData.logo_url,
            competitionData.api_id
          ])
          
          console.log(`✅ Competição criada: ${comp.name} - Escudo: ${comp.emblem}`)
          syncCount++
        } else {
          // Atualizar competição existente com escudo real
          await executeQuery(`
            UPDATE campeonatos SET
              nome = ?, descricao = ?, pais = ?, logo_url = ?
            WHERE api_id = ?
          `, [
            competitionData.nome,
            competitionData.descricao,
            competitionData.pais,
            competitionData.logo_url,
            comp.id
          ])
          
          console.log(`🔄 Competição atualizada: ${comp.name} - Escudo: ${comp.emblem}`)
        }
        
        // Delay para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 200))
        
      } catch (error) {
        console.error(`❌ Erro ao processar competição ${comp.name}:`, error.message)
      }
    }
    
    console.log(`🏆 ${syncCount} competições sincronizadas com escudos reais`)
    return syncCount
    
  } catch (error) {
    console.error('❌ Erro na sincronização de competições:', error.message)
    return 0
  }
}

/**
 * Sincroniza times com escudos reais de uma competição
 */
async function syncRealTeams(competitionId) {
  try {
    console.log(`👥 Sincronizando times com escudos reais da competição ${competitionId}...`)
    
    const data = await fetchFootballData(`/competitions/${competitionId}/teams`)
    if (!data || !data.teams) {
      console.log(`❌ Nenhum time encontrado para competição ${competitionId}`)
      return 0
    }
    
    const teams = data.teams
    let syncCount = 0
    
    for (const team of teams) {
      try {
        // Verificar se já existe
        const existing = await executeQuery(
          'SELECT id FROM times WHERE api_id = ?',
          [team.id]
        )
        
        const teamData = {
          nome: team.name,
          nome_curto: team.shortName || team.tla || team.name,
          pais: team.area?.name || 'Desconhecido',
          logo_url: team.crest || null, // Escudo real da API (https://crests.football-data.org/)
          api_id: team.id
        }
        
        if (existing.length === 0) {
          // Inserir novo time
          await executeQuery(`
            INSERT INTO times (
              nome, nome_curto, pais, logo_url, api_id, codigo, data_criacao
            ) VALUES (?, ?, ?, ?, ?, ?, NOW())
          `, [
            teamData.nome,
            teamData.nome_curto,
            teamData.pais,
            teamData.logo_url,
            teamData.api_id,
            teamData.codigo
          ])
          
          console.log(`✅ Time criado: ${team.name} (${team.shortName}) - Escudo: ${team.crest}`)
          syncCount++
        } else {
          // Atualizar time existente com escudo real
          await executeQuery(`
            UPDATE times SET
              nome = ?, nome_curto = ?, pais = ?, logo_url = ?
            WHERE api_id = ?
          `, [
            teamData.nome,
            teamData.nome_curto,
            teamData.pais,
            teamData.logo_url,
            team.id
          ])
          
          console.log(`🔄 Time atualizado: ${team.name} (${team.shortName}) - Escudo: ${team.crest}`)
        }
        
        // Delay para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 200))
        
      } catch (error) {
        console.error(`❌ Erro ao processar time ${team.name}:`, error.message)
      }
    }
    
    console.log(`👥 ${syncCount} times sincronizados com escudos reais`)
    return syncCount
    
  } catch (error) {
    console.error(`❌ Erro na sincronização de times da competição ${competitionId}:`, error.message)
    return 0
  }
}

/**
 * Execução principal
 */
async function main() {
  try {
    console.log('🚀 Iniciando sincronização com escudos reais da football-data.org...')
    
    await initializeDatabase()
    
    // 1. Sincronizar competições com escudos reais
    const competitionsCount = await syncRealCompetitions()
    console.log(`🏆 Total de competições: ${competitionsCount}`)
    
    // 2. Competições prioritárias para sincronizar times
    const priorityCompetitions = [
      2013, // Brasileirão Série A
      2021, // Premier League
      2014, // La Liga
      2019, // Serie A
      2002, // Bundesliga
      2015, // Ligue 1
      2001, // Champions League
      2018, // Europa League
    ]
    
    let totalTeams = 0
    
    // 3. Sincronizar times com escudos reais para cada competição
    for (const compId of priorityCompetitions) {
      console.log(`\n🎯 Processando competição: ${compId}`)
      
      const teamsCount = await syncRealTeams(compId)
      totalTeams += teamsCount
      
      // Delay entre competições
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
    
    console.log('\n🎉 Sincronização com escudos reais completa!')
    console.log(`📊 Resumo:`)
    console.log(`   🏆 Competições: ${competitionsCount}`)
    console.log(`   👥 Times: ${totalTeams}`)
    
    // Verificar estatísticas finais
    const [stats] = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN logo_url IS NOT NULL AND logo_url != '' THEN 1 ELSE 0 END) as com_logo,
        SUM(CASE WHEN logo_url LIKE '%crests.football-data.org%' THEN 1 ELSE 0 END) as com_escudo_real
      FROM times
    `)
    
    console.log(`\n📊 Estatísticas de escudos:`)
    console.log(`   📈 Total de times: ${stats.total}`)
    console.log(`   ✅ Times com logo: ${stats.com_logo}`)
    console.log(`   🏆 Times com escudo real: ${stats.com_escudo_real}`)
    
    // Mostrar exemplos de times com escudos reais
    const exemplos = await executeQuery(`
      SELECT nome, nome_curto, logo_url FROM times 
      WHERE logo_url LIKE '%crests.football-data.org%'
      ORDER BY nome
      LIMIT 10
    `)
    
    console.log(`\n🎨 Exemplos de times com escudos reais:`)
    exemplos.forEach(time => {
      console.log(`   ${time.nome_curto} -> ${time.logo_url}`)
    })
    
  } catch (error) {
    console.error('❌ Erro na sincronização:', error.message)
  }
}

// Executar
main()
